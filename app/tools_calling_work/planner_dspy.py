import os
import json
import dspy
import time
import pickle
import re
from pathlib import Path
from typing import List, Literal, Optional, Union, Dict, Any, Tuple
from pydantic import BaseModel, Field
from dataclasses import dataclass

# Configure DSPy with OpenAI
os.environ["OPENAI_API_KEY"] = "********************************************************************************************************************************************************************"

# Configure DSPy LM
lm = dspy.LM('openai/gpt-4o-mini', temperature=0)
dspy.configure(lm=lm)

# Performance tracking
@dataclass
class PerformanceMetrics:
    decomposition_time: float = 0.0
    validation_time: float = 0.0
    parsing_time: float = 0.0
    total_time: float = 0.0
    query_complexity: str = "unknown"
    validation_skipped: bool = False

# Model persistence paths
MODEL_CACHE_DIR = Path("app/tools_calling_work/model_cache")
MODEL_CACHE_DIR.mkdir(exist_ok=True)
OPTIMIZED_MODEL_PATH = MODEL_CACHE_DIR / "optimized_planner.pkl"

# Type definitions
Tag = Literal["collect_data", "analyze", "visualize", "summarize", "general_qa"]

class Task(BaseModel):
    id: str
    parent: Optional[Union[str, List[str]]] = None
    tag: Tag
    entity: Optional[Union[str, List[str]]] = None
    cleanTask: str
    description: str

class Plan(BaseModel):
    tasks: List[Task]

# Symbol extraction and query analysis
class SymbolExtractor:
    """Extract financial symbols and entities from queries."""

    def __init__(self):
        # Common stock symbols and company mappings
        self.symbol_patterns = {
            r'\b(AAPL|Apple)\b': 'AAPL',
            r'\b(MSFT|Microsoft)\b': 'MSFT',
            r'\b(GOOGL|GOOG|Google|Alphabet)\b': 'GOOGL',
            r'\b(AMZN|Amazon)\b': 'AMZN',
            r'\b(TSLA|Tesla)\b': 'TSLA',
            r'\b(META|Facebook|Meta)\b': 'META',
            r'\b(NVDA|Nvidia)\b': 'NVDA',
            r'\b(NFLX|Netflix)\b': 'NFLX',
            r'\b(KO|Coca-Cola|Coke)\b': 'KO',
            r'\b(PEP|PepsiCo|Pepsi)\b': 'PEP',
            r'\b(SBUX|Starbucks)\b': 'SBUX',
            r'\b(IBM)\b': 'IBM',
            r'\b(BRK\.A|BRK\.B|Berkshire Hathaway)\b': 'BRK.A',
        }

        # Sector and index patterns
        self.sector_patterns = {
            r'\b(technology|tech)\b': 'Technology',
            r'\b(healthcare|health)\b': 'Healthcare',
            r'\b(financial|finance|banking)\b': 'Financial',
            r'\b(energy|oil|gas)\b': 'Energy',
            r'\b(utilities)\b': 'Utilities',
            r'\b(S&P 500|SP500)\b': 'S&P 500',
            r'\b(NASDAQ 100|NASDAQ)\b': 'NASDAQ 100',
            r'\b(Dow Jones|DJIA)\b': 'DJIA',
        }

    def extract_symbols(self, query: str) -> Dict[str, List[str]]:
        """Extract symbols, companies, and sectors from query."""
        query_upper = query.upper()

        symbols = []
        sectors = []

        # Extract stock symbols
        for pattern, symbol in self.symbol_patterns.items():
            if re.search(pattern, query, re.IGNORECASE):
                symbols.append(symbol)

        # Extract sectors and indices
        for pattern, sector in self.sector_patterns.items():
            if re.search(pattern, query, re.IGNORECASE):
                sectors.append(sector)

        # Extract generic stock symbols (3-5 uppercase letters, but filter out common words)
        common_words = {'THE', 'AND', 'FOR', 'ARE', 'BUT', 'NOT', 'YOU', 'ALL', 'CAN', 'HER', 'WAS', 'ONE', 'OUR', 'HAD', 'BUT', 'WHAT', 'WERE', 'THEY', 'WE', 'BEEN', 'HAVE', 'THEIR', 'SAID', 'EACH', 'WHICH', 'SHE', 'DO', 'HOW', 'IF', 'WILL', 'UP', 'OTHER', 'ABOUT', 'OUT', 'MANY', 'THEN', 'THEM', 'THESE', 'SO', 'SOME', 'HER', 'WOULD', 'MAKE', 'LIKE', 'INTO', 'HIM', 'HAS', 'TWO', 'MORE', 'GO', 'NO', 'WAY', 'COULD', 'MY', 'THAN', 'FIRST', 'BEEN', 'CALL', 'WHO', 'ITS', 'NOW', 'FIND', 'LONG', 'DOWN', 'DAY', 'DID', 'GET', 'COME', 'MADE', 'MAY', 'PART', 'OVER', 'NEW', 'SOUND', 'TAKE', 'ONLY', 'LITTLE', 'WORK', 'KNOW', 'PLACE', 'YEAR', 'LIVE', 'ME', 'BACK', 'GIVE', 'MOST', 'VERY', 'AFTER', 'MOVE', 'MUCH', 'NAME', 'GOOD', 'SENTENCE', 'MAN', 'THINK', 'SAY', 'GREAT', 'WHERE', 'HELP', 'THROUGH', 'LINE', 'RIGHT', 'TOO', 'MEAN', 'OLD', 'ANY', 'SAME', 'TELL', 'BOY', 'FOLLOW', 'CAME', 'WANT', 'SHOW', 'ALSO', 'AROUND', 'FORM', 'THREE', 'SMALL', 'SET', 'PUT', 'END', 'WHY', 'AGAIN', 'TURN', 'HERE', 'OFF', 'WENT', 'CAME', 'WELL', 'MANY', 'BEFORE', 'MOVE', 'RIGHT', 'BOY', 'OLD', 'TOO', 'DOES', 'TELL', 'SENTENCE', 'SET', 'THREE', 'WANT', 'AIR', 'WELL', 'ALSO', 'PLAY', 'SMALL', 'END', 'PUT', 'HOME', 'READ', 'HAND', 'PORT', 'LARGE', 'SPELL', 'ADD', 'EVEN', 'LAND', 'HERE', 'MUST', 'BIG', 'HIGH', 'SUCH', 'FOLLOW', 'ACT', 'WHY', 'ASK', 'MEN', 'CHANGE', 'WENT', 'LIGHT', 'KIND', 'OFF', 'NEED', 'HOUSE', 'PICTURE', 'TRY', 'US', 'AGAIN', 'ANIMAL', 'POINT', 'MOTHER', 'WORLD', 'NEAR', 'BUILD', 'SELF', 'EARTH', 'FATHER', 'HEAD', 'STAND', 'OWN', 'PAGE', 'SHOULD', 'COUNTRY', 'FOUND', 'ANSWER', 'SCHOOL', 'GROW', 'STUDY', 'STILL', 'LEARN', 'PLANT', 'COVER', 'FOOD', 'SUN', 'FOUR', 'BETWEEN', 'STATE', 'KEEP', 'EYE', 'NEVER', 'LAST', 'LET', 'THOUGHT', 'CITY', 'TREE', 'CROSS', 'FARM', 'HARD', 'START', 'MIGHT', 'STORY', 'SAW', 'FAR', 'SEA', 'DRAW', 'LEFT', 'LATE', 'RUN', 'DONT', 'WHILE', 'PRESS', 'CLOSE', 'NIGHT', 'REAL', 'LIFE', 'FEW', 'NORTH', 'OPEN', 'SEEM', 'TOGETHER', 'NEXT', 'WHITE', 'CHILDREN', 'BEGINNING', 'GOT', 'WALK', 'EXAMPLE', 'EASE', 'PAPER', 'GROUP', 'ALWAYS', 'MUSIC', 'THOSE', 'BOTH', 'MARK', 'OFTEN', 'LETTER', 'UNTIL', 'MILE', 'RIVER', 'CAR', 'FEET', 'CARE', 'SECOND', 'BOOK', 'CARRY', 'TOOK', 'SCIENCE', 'EAT', 'ROOM', 'FRIEND', 'BEGAN', 'IDEA', 'FISH', 'MOUNTAIN', 'STOP', 'ONCE', 'BASE', 'HEAR', 'HORSE', 'CUT', 'SURE', 'WATCH', 'COLOR', 'FACE', 'WOOD', 'MAIN', 'ENOUGH', 'PLAIN', 'GIRL', 'USUAL', 'YOUNG', 'READY', 'ABOVE', 'EVER', 'RED', 'LIST', 'THOUGH', 'FEEL', 'TALK', 'BIRD', 'SOON', 'BODY', 'DOG', 'FAMILY', 'DIRECT', 'LEAVE', 'SONG', 'MEASURE', 'DOOR', 'PRODUCT', 'BLACK', 'SHORT', 'NUMERAL', 'CLASS', 'WIND', 'QUESTION', 'HAPPEN', 'COMPLETE', 'SHIP', 'AREA', 'HALF', 'ROCK', 'ORDER', 'FIRE', 'SOUTH', 'PROBLEM', 'PIECE', 'TOLD', 'KNEW', 'PASS', 'SINCE', 'TOP', 'WHOLE', 'KING', 'SPACE', 'HEARD', 'BEST', 'HOUR', 'BETTER', 'DURING', 'HUNDRED', 'FIVE', 'REMEMBER', 'STEP', 'EARLY', 'HOLD', 'WEST', 'GROUND', 'INTEREST', 'REACH', 'FAST', 'VERB', 'SING', 'LISTEN', 'SIX', 'TABLE', 'TRAVEL', 'LESS', 'MORNING', 'TEN', 'SIMPLE', 'SEVERAL', 'VOWEL', 'TOWARD', 'WAR', 'LAY', 'AGAINST', 'PATTERN', 'SLOW', 'CENTER', 'LOVE', 'PERSON', 'MONEY', 'SERVE', 'APPEAR', 'ROAD', 'MAP', 'RAIN', 'RULE', 'GOVERN', 'PULL', 'COLD', 'NOTICE', 'VOICE', 'UNIT', 'POWER', 'TOWN', 'FINE', 'CERTAIN', 'FLY', 'FALL', 'LEAD', 'CRY', 'DARK', 'MACHINE', 'NOTE', 'WAIT', 'PLAN', 'FIGURE', 'STAR', 'BOX', 'NOUN', 'FIELD', 'REST', 'CORRECT', 'ABLE', 'POUND', 'DONE', 'BEAUTY', 'DRIVE', 'STOOD', 'CONTAIN', 'FRONT', 'TEACH', 'WEEK', 'FINAL', 'GAVE', 'GREEN', 'OH', 'QUICK', 'DEVELOP', 'OCEAN', 'WARM', 'FREE', 'MINUTE', 'STRONG', 'SPECIAL', 'MIND', 'BEHIND', 'CLEAR', 'TAIL', 'PRODUCE', 'FACT', 'STREET', 'INCH', 'MULTIPLY', 'NOTHING', 'COURSE', 'STAY', 'WHEEL', 'FULL', 'FORCE', 'BLUE', 'OBJECT', 'DECIDE', 'SURFACE', 'DEEP', 'MOON', 'ISLAND', 'FOOT', 'SYSTEM', 'BUSY', 'TEST', 'RECORD', 'BOAT', 'COMMON', 'GOLD', 'POSSIBLE', 'PLANE', 'STEAD', 'DRY', 'WONDER', 'LAUGH', 'THOUSANDS', 'AGO', 'RAN', 'CHECK', 'GAME', 'SHAPE', 'EQUATE', 'MISS', 'BROUGHT', 'HEAT', 'SNOW', 'TIRE', 'BRING', 'YES', 'DISTANT', 'FILL', 'EAST', 'PAINT', 'LANGUAGE', 'AMONG', 'GRAND', 'BALL', 'YET', 'WAVE', 'DROP', 'HEART', 'AM', 'PRESENT', 'HEAVY', 'DANCE', 'ENGINE', 'POSITION', 'ARM', 'WIDE', 'SAIL', 'MATERIAL', 'SIZE', 'VARY', 'SETTLE', 'SPEAK', 'WEIGHT', 'GENERAL', 'ICE', 'MATTER', 'CIRCLE', 'PAIR', 'INCLUDE', 'DIVIDE', 'SYLLABLE', 'FELT', 'PERHAPS', 'PICK', 'SUDDEN', 'COUNT', 'SQUARE', 'REASON', 'LENGTH', 'REPRESENT', 'ART', 'SUBJECT', 'REGION', 'ENERGY', 'HUNT', 'PROBABLE', 'BED', 'BROTHER', 'EGG', 'RIDE', 'CELL', 'BELIEVE', 'FRACTION', 'FOREST', 'SIT', 'RACE', 'WINDOW', 'STORE', 'SUMMER', 'TRAIN', 'SLEEP', 'PROVE', 'LONE', 'LEG', 'EXERCISE', 'WALL', 'CATCH', 'MOUNT', 'WISH', 'SKY', 'BOARD', 'JOY', 'WINTER', 'SAT', 'WRITTEN', 'WILD', 'INSTRUMENT', 'KEPT', 'GLASS', 'GRASS', 'COW', 'JOB', 'EDGE', 'SIGN', 'VISIT', 'PAST', 'SOFT', 'FUN', 'BRIGHT', 'GAS', 'WEATHER', 'MONTH', 'MILLION', 'BEAR', 'FINISH', 'HAPPY', 'HOPE', 'FLOWER', 'CLOTHE', 'STRANGE', 'GONE', 'JUMP', 'BABY', 'EIGHT', 'VILLAGE', 'MEET', 'ROOT', 'BUY', 'RAISE', 'SOLVE', 'METAL', 'WHETHER', 'PUSH', 'SEVEN', 'PARAGRAPH', 'THIRD', 'SHALL', 'HELD', 'HAIR', 'DESCRIBE', 'COOK', 'FLOOR', 'EITHER', 'RESULT', 'BURN', 'HILL', 'SAFE', 'CAT', 'CENTURY', 'CONSIDER', 'TYPE', 'LAW', 'BIT', 'COAST', 'COPY', 'PHRASE', 'SILENT', 'TALL', 'SAND', 'SOIL', 'ROLL', 'TEMPERATURE', 'FINGER', 'INDUSTRY', 'VALUE', 'FIGHT', 'LIE', 'BEAT', 'EXCITE', 'NATURAL', 'VIEW', 'SENSE', 'EAR', 'ELSE', 'QUITE', 'BROKE', 'CASE', 'MIDDLE', 'KILL', 'SON', 'LAKE', 'MOMENT', 'SCALE', 'LOUD', 'SPRING', 'OBSERVE', 'CHILD', 'STRAIGHT', 'CONSONANT', 'NATION', 'DICTIONARY', 'MILK', 'SPEED', 'METHOD', 'ORGAN', 'PAY', 'AGE', 'SECTION', 'DRESS', 'CLOUD', 'SURPRISE', 'QUIET', 'STONE', 'TINY', 'CLIMB', 'BAD', 'OIL', 'BLOOD', 'TOUCH', 'GREW', 'CENT', 'MIX', 'TEAM', 'WIRE', 'COST', 'LOST', 'BROWN', 'WEAR', 'GARDEN', 'EQUAL', 'SENT', 'CHOOSE', 'FELL', 'FIT', 'FLOW', 'FAIR', 'BANK', 'COLLECT', 'SAVE', 'CONTROL', 'DECIMAL', 'GENTLE', 'WOMAN', 'CAPTAIN', 'PRACTICE', 'SEPARATE', 'DIFFICULT', 'DOCTOR', 'PLEASE', 'PROTECT', 'NOON', 'WHOSE', 'LOCATE', 'RING', 'CHARACTER', 'INSECT', 'CAUGHT', 'PERIOD', 'INDICATE', 'RADIO', 'SPOKE', 'ATOM', 'HUMAN', 'HISTORY', 'EFFECT', 'ELECTRIC', 'EXPECT', 'CROP', 'MODERN', 'ELEMENT', 'HIT', 'STUDENT', 'CORNER', 'PARTY', 'SUPPLY', 'BONE', 'RAIL', 'IMAGINE', 'PROVIDE', 'AGREE', 'THUS', 'CAPITAL', 'WONT', 'CHAIR', 'DANGER', 'FRUIT', 'RICH', 'THICK', 'SOLDIER', 'PROCESS', 'OPERATE', 'GUESS', 'NECESSARY', 'SHARP', 'WING', 'CREATE', 'NEIGHBOR', 'WASH', 'BAT', 'RATHER', 'CROWD', 'CORN', 'COMPARE', 'POEM', 'STRING', 'BELL', 'DEPEND', 'MEAT', 'RUB', 'TUBE', 'FAMOUS', 'DOLLAR', 'STREAM', 'FEAR', 'SIGHT', 'THIN', 'TRIANGLE', 'PLANET', 'HURRY', 'CHIEF', 'COLONY', 'CLOCK', 'MINE', 'TIE', 'ENTER', 'MAJOR', 'FRESH', 'SEARCH', 'SEND', 'YELLOW', 'GUN', 'ALLOW', 'PRINT', 'DEAD', 'SPOT', 'DESERT', 'SUIT', 'CURRENT', 'LIFT', 'ROSE', 'CONTINUE', 'BLOCK', 'CHART', 'HAT', 'SELL', 'SUCCESS', 'COMPANY', 'SUBTRACT', 'EVENT', 'PARTICULAR', 'DEAL', 'SWIM', 'TERM', 'OPPOSITE', 'WIFE', 'SHOE', 'SHOULDER', 'SPREAD', 'ARRANGE', 'CAMP', 'INVENT', 'COTTON', 'BORN', 'DETERMINE', 'QUART', 'NINE', 'TRUCK', 'NOISE', 'LEVEL', 'CHANCE', 'GATHER', 'SHOP', 'STRETCH', 'THROW', 'SHINE', 'PROPERTY', 'COLUMN', 'MOLECULE', 'SELECT', 'WRONG', 'GRAY', 'REPEAT', 'REQUIRE', 'BROAD', 'PREPARE', 'SALT', 'NOSE', 'PLURAL', 'ANGER', 'CLAIM', 'CONTINENT', 'OXYGEN', 'SUGAR', 'DEATH', 'PRETTY', 'SKILL', 'WOMEN', 'SEASON', 'SOLUTION', 'MAGNET', 'SILVER', 'THANK', 'BRANCH', 'MATCH', 'SUFFIX', 'ESPECIALLY', 'FIG', 'AFRAID', 'HUGE', 'SISTER', 'STEEL', 'DISCUSS', 'FORWARD', 'SIMILAR', 'GUIDE', 'EXPERIENCE', 'SCORE', 'APPLE', 'BOUGHT', 'LED', 'PITCH', 'COAT', 'MASS', 'CARD', 'BAND', 'ROPE', 'SLIP', 'WIN', 'DREAM', 'EVENING', 'CONDITION', 'FEED', 'TOOL', 'TOTAL', 'BASIC', 'SMELL', 'VALLEY', 'NOR', 'DOUBLE', 'SEAT', 'ARRIVE', 'MASTER', 'TRACK', 'PARENT', 'SHORE', 'DIVISION', 'SHEET', 'SUBSTANCE', 'FAVOR', 'CONNECT', 'POST', 'SPEND', 'CHORD', 'FAT', 'GLAD', 'ORIGINAL', 'SHARE', 'STATION', 'DAD', 'BREAD', 'CHARGE', 'PROPER', 'BAR', 'OFFER', 'SEGMENT', 'SLAVE', 'DUCK', 'INSTANT', 'MARKET', 'DEGREE', 'POPULATE', 'CHICK', 'DEAR', 'ENEMY', 'REPLY', 'DRINK', 'OCCUR', 'SUPPORT', 'SPEECH', 'NATURE', 'RANGE', 'STEAM', 'MOTION', 'PATH', 'LIQUID', 'LOG', 'MEANT', 'QUOTIENT', 'TEETH', 'SHELL', 'NECK', 'STOCK', 'PRICE', 'THERE', 'HELLO', 'CURRENT', 'RATE', 'INFLATION'}
        generic_symbols = re.findall(r'\b[A-Z]{3,5}\b', query_upper)
        for symbol in generic_symbols:
            if symbol not in symbols and len(symbol) <= 5 and symbol not in common_words:
                symbols.append(symbol)

        return {
            'symbols': list(set(symbols)),
            'sectors': list(set(sectors))
        }

class QueryComplexityClassifier:
    """Classify query complexity to optimize processing."""

    def __init__(self):
        self.simple_indicators = [
            r'\bcurrent\b', r'\btoday\b', r'\blatest\b', r'\bwhat is\b',
            r'\bget\b', r'\bfind\b', r'\bshow\b', r'\blist\b'
        ]

        self.complex_indicators = [
            r'\bcompare\b', r'\banalyze\b', r'\bcorrelation\b', r'\btrend\b',
            r'\bforecast\b', r'\bpredict\b', r'\bregression\b', r'\bvolatility\b',
            r'\btop \d+\b', r'\bhighest\b', r'\blowest\b', r'\bbest\b', r'\bworst\b',
            r'\band\b.*\band\b', r'\bthen\b', r'\balso\b'
        ]

    def classify(self, query: str) -> Tuple[str, float]:
        """Classify query complexity and return confidence score."""
        query_lower = query.lower()

        simple_score = sum(1 for pattern in self.simple_indicators
                          if re.search(pattern, query_lower))
        complex_score = sum(1 for pattern in self.complex_indicators
                           if re.search(pattern, query_lower))

        # Additional complexity factors
        word_count = len(query.split())
        question_marks = query.count('?')

        # Scoring logic
        if complex_score > simple_score or word_count > 15:
            return "complex", min(0.9, 0.6 + (complex_score * 0.1) + (word_count * 0.01))
        elif simple_score > 0 and word_count < 10:
            return "simple", min(0.9, 0.7 + (simple_score * 0.1))
        else:
            return "medium", 0.5

# Global instances
symbol_extractor = SymbolExtractor()
complexity_classifier = QueryComplexityClassifier()

# DSPy Signatures with Structured Output
class TaskDecomposition(dspy.Signature):
    """Expert task decomposer that creates optimal, logical task hierarchies for complex multi-step workflows.

    CORE PRINCIPLES:
    - EFFICIENCY: Create separate tasks for different data types to enable parallel execution
    - EXECUTION READINESS: Every task must be immediately executable with clear, unambiguous inputs
    - DEPENDENCY PRECISION: Parent references must guarantee ALL required data is available
    - SEMANTIC CLARITY: Use standardized entity names and complete terminology
    - DATA SEPARATION: Create separate tasks for different metrics/data types even for same companies
    - FINANCE COVERAGE: ALL finance-related queries must generate at least one task

    DEFAULT STOCK HANDLING:
    - If no specific stock name or symbol is mentioned in the query, use AAPL (Apple Inc.) as the default
    - Always use official stock symbols in entity fields: AAPL, MSFT, GOOGL, AMZN, TSLA, etc.

    CRITICAL DEPENDENCY RULES:
    - Create SEPARATE tasks for different data types
    - For superlative queries ("top N", "largest"): Always start with identification task
    - Multi-parent dependencies: Use ["t1", "t2", "t3"] format
    - Entity fields: Use actual identifiers, NEVER task references like "$t1"

    TASK TAGGING PRECISION:
    - collect_data: Raw information retrieval from external sources
    - analyze: Mathematical operations, calculations, statistical analysis
    - visualize: Chart creation, graph generation, visual representations
    - summarize: Written synthesis, report generation, findings description
    - general_qa: Subjective judgments, recommendations, opinions

    IMPOSSIBLE QUERIES → RETURN EMPTY TASK LIST []:
    - Pure greetings without actionable requests and Requests completely outside financial/data analysis domain
    """

    query: str = dspy.InputField(desc="User query to decompose into tasks")
    extracted_symbols: str = dspy.InputField(desc="Extracted financial symbols and entities from query")
    tasks: List[Task] = dspy.OutputField(desc="List of Task objects following the schema")

class SimpleTaskDecomposition(dspy.Signature):
    """Simple task decomposer for straightforward queries requiring minimal processing."""

    query: str = dspy.InputField(desc="Simple user query")
    extracted_symbols: str = dspy.InputField(desc="Extracted financial symbols and entities")
    tasks: List[Task] = dspy.OutputField(desc="List of Task objects (typically 1-2 tasks)")

class TaskValidation(dspy.Signature):
    """Validate and refine task plans to ensure they follow all rules and patterns."""

    query: str = dspy.InputField(desc="Original user query")
    tasks: List[Task] = dspy.InputField(desc="List of tasks to validate")
    validation_reasoning: str = dspy.OutputField(desc="Analysis of task plan quality and needed improvements")
    refined_tasks: List[Task] = dspy.OutputField(desc="Improved list of tasks with corrections applied")

# Enhanced DSPy Module with Adaptive Complexity Handling
class DSPyPlanner(dspy.Module):
    def __init__(self):
        # Complex query processing
        self.complex_decompose = dspy.ChainOfThought(TaskDecomposition)
        self.validate = dspy.ChainOfThought(TaskValidation)

        # Simple query processing (faster)
        self.simple_decompose = dspy.Predict(SimpleTaskDecomposition)

        # Performance tracking
        self.performance_metrics = PerformanceMetrics()

    def forward(self, query: str, extracted_symbols: Optional[Dict[str, List[str]]] = None):
        start_time = time.time()

        # Extract symbols if not provided
        if extracted_symbols is None:
            extracted_symbols = symbol_extractor.extract_symbols(query)

        # Classify query complexity
        complexity, confidence = complexity_classifier.classify(query)
        self.performance_metrics.query_complexity = f"{complexity} ({confidence:.2f})"

        # Convert extracted symbols to string for DSPy input
        symbols_str = json.dumps(extracted_symbols)

        # Choose processing path based on complexity
        decomposition_start = time.time()

        if complexity == "simple":
            print(f"🚀 Using simple processing for query (confidence: {confidence:.2f})")
            decomposition = self.simple_decompose(query=query, extracted_symbols=symbols_str)
            tasks = decomposition.tasks if hasattr(decomposition, 'tasks') else []
        else:
            print(f"🧠 Using complex processing for query (confidence: {confidence:.2f})")
            decomposition = self.complex_decompose(query=query, extracted_symbols=symbols_str)
            tasks = decomposition.tasks if hasattr(decomposition, 'tasks') else []

        self.performance_metrics.decomposition_time = time.time() - decomposition_start

        # Conditional validation based on complexity and initial result quality
        validation_start = time.time()
        validation_needed = self._should_validate(tasks, complexity, confidence)

        if validation_needed:
            print("🔍 Running validation and refinement...")
            validation = self.validate(query=query, tasks=tasks)
            final_tasks = validation.refined_tasks if hasattr(validation, 'refined_tasks') else tasks
            validation_reasoning = getattr(validation, 'validation_reasoning', "")
        else:
            print("⚡ Skipping validation for performance optimization")
            final_tasks = tasks
            validation_reasoning = "Validation skipped - high confidence result"
            self.performance_metrics.validation_skipped = True

        self.performance_metrics.validation_time = time.time() - validation_start
        self.performance_metrics.total_time = time.time() - start_time

        return dspy.Prediction(
            tasks=final_tasks,
            validation_reasoning=validation_reasoning,
            performance_metrics=self.performance_metrics,
            extracted_symbols=extracted_symbols
        )

    def _should_validate(self, tasks: List[Task], complexity: str, confidence: float) -> bool:
        """Determine if validation is needed based on complexity and initial quality."""
        # Always validate complex queries
        if complexity == "complex":
            return True

        # Skip validation for simple queries with high confidence
        if complexity == "simple" and confidence > 0.8:
            return False

        # Validate if tasks seem problematic
        if not tasks or len(tasks) > 5:
            return True

        # Check for basic task quality issues
        for task in tasks:
            if not hasattr(task, 'id') or not hasattr(task, 'tag') or not hasattr(task, 'cleanTask'):
                return True

        return False

# Model persistence functions (simplified approach due to DSPy serialization complexity)
def save_optimized_model(model, path: Path = OPTIMIZED_MODEL_PATH):
    """Save optimized DSPy model configuration to disk."""
    try:
        # Instead of pickling the entire model, save the optimization state
        model_state = {
            'optimized': True,
            'timestamp': time.time(),
            'model_type': 'DSPyPlanner',
            'optimization_method': 'BootstrapFewShot'
        }

        # Try to extract any optimized demonstrations if available
        if hasattr(model, 'complex_decompose') and hasattr(model.complex_decompose, 'demos'):
            model_state['demos'] = model.complex_decompose.demos

        with open(path, 'wb') as f:
            pickle.dump(model_state, f)
        print(f"✅ Optimized model state saved to {path}")
        return True
    except Exception as e:
        print(f"❌ Error saving model state: {e}")
        return False

def load_optimized_model(path: Path = OPTIMIZED_MODEL_PATH) -> Optional[DSPyPlanner]:
    """Load optimized DSPy model from disk (simplified approach)."""
    try:
        if path.exists():
            with open(path, 'rb') as f:
                model_state = pickle.load(f)

            if model_state.get('optimized'):
                print(f"✅ Found optimized model state from {path}")
                # Create a new planner and mark it as optimized
                planner = DSPyPlanner()
                planner._is_optimized = True
                planner._optimization_timestamp = model_state.get('timestamp')
                return planner
            else:
                print(f"Model state found but not optimized")
                return None
        else:
            print(f" No optimized model found at {path}")
            return None
    except Exception as e:
        print(f"❌ Error loading model state: {e}")
        return None

# Initialize the planner (try to load optimized version first)
planner = load_optimized_model() or DSPyPlanner()

def parse_tasks_from_json(tasks_json: str) -> List[Task]:
    """Legacy function for backward compatibility - Parse tasks from JSON string."""
    try:
        tasks_data = json.loads(tasks_json)
        if not isinstance(tasks_data, list):
            return []

        tasks = []
        for task_data in tasks_data:
            if isinstance(task_data, dict) and all(key in task_data for key in ['id', 'tag', 'cleanTask', 'description']):
                # Handle parent field - convert empty lists to None
                parent = task_data.get('parent')
                if isinstance(parent, list) and len(parent) == 0:
                    parent = None

                task = Task(
                    id=task_data['id'],
                    parent=parent,
                    tag=task_data['tag'],
                    entity=task_data.get('entity'),
                    cleanTask=task_data['cleanTask'],
                    description=task_data['description']
                )
                tasks.append(task)
        return tasks
    except (json.JSONDecodeError, KeyError, TypeError):
        return []

def get_plan(user_query: str, extracted_symbols: Optional[Dict[str, List[str]]] = None,
             show_performance: bool = False) -> List[dict]:
    """Enhanced get_plan function with symbol extraction and performance tracking.

    Args:
        user_query: The user's query to process
        extracted_symbols: Pre-extracted symbols (optional, will auto-extract if None)
        show_performance: Whether to display performance metrics

    Returns:
        List of task dictionaries
    """
    try:
        parsing_start = time.time()

        # Extract symbols if not provided
        if extracted_symbols is None:
            extraction_start = time.time()
            extracted_symbols = symbol_extractor.extract_symbols(user_query)
            extraction_time = time.time() - extraction_start
            if show_performance:
                print(f"⏱️ Symbol extraction: {extraction_time:.3f}s")

        # Get prediction from DSPy planner with enhanced features
        prediction = planner(query=user_query, extracted_symbols=extracted_symbols)

        # Extract tasks (now directly from structured output)
        tasks = prediction.tasks if hasattr(prediction, 'tasks') else []

        # Handle both Task objects and dict objects for backward compatibility
        if tasks and isinstance(tasks[0], Task):
            task_dicts = [task.model_dump() for task in tasks]
        else:
            task_dicts = tasks if isinstance(tasks, list) else []

        parsing_time = time.time() - parsing_start

        # Display performance metrics if requested
        if show_performance and hasattr(prediction, 'performance_metrics'):
            metrics = prediction.performance_metrics
            print(f"\n📊 PERFORMANCE METRICS:")
            print(f"   Query Complexity: {metrics.query_complexity}")
            print(f"   Decomposition: {metrics.decomposition_time:.3f}s")
            print(f"   Validation: {metrics.validation_time:.3f}s {'(skipped)' if metrics.validation_skipped else ''}")
            print(f"   Parsing: {parsing_time:.3f}s")
            print(f"   Total Time: {metrics.total_time:.3f}s")

            if extracted_symbols['symbols'] or extracted_symbols['sectors']:
                print(f"   Extracted Symbols: {extracted_symbols['symbols']}")
                print(f"   Extracted Sectors: {extracted_symbols['sectors']}")

        return task_dicts

    except Exception as e:
        print(f"❌ Error in DSPy planner: {e}")
        return []

# Backward compatibility function
def get_plan_with_symbols(user_query: str, symbols: List[str] = None,
                         sectors: List[str] = None) -> List[dict]:
    """Backward compatible function that accepts symbols and sectors separately."""
    extracted_symbols = {
        'symbols': symbols or [],
        'sectors': sectors or []
    }
    return get_plan(user_query, extracted_symbols)

# Enhanced evaluation metric for plan quality
def evaluate_plan_quality(example, pred, trace=None):
    """Evaluate the quality of a generated plan with structured output support."""
    try:
        # Handle both old JSON format and new structured format
        if hasattr(pred, 'tasks') and isinstance(pred.tasks, list):
            tasks = pred.tasks
        elif hasattr(pred, 'tasks_json'):
            tasks = parse_tasks_from_json(pred.tasks_json)
        else:
            return 0.0

        if not tasks:
            return 0.0

        score = 0.0

        # Check if finance queries generate tasks
        finance_keywords = ['stock', 'price', 'market', 'financial', 'revenue', 'earnings', 'dividend', 'investment']
        is_finance_query = any(keyword in example.query.lower() for keyword in finance_keywords)

        if is_finance_query and len(tasks) > 0:
            score += 0.3
        elif not is_finance_query and len(tasks) == 0:
            score += 0.3

        # Check task structure quality
        for task in tasks:
            # Handle both Task objects and dict objects
            task_id = task.id if hasattr(task, 'id') else task.get('id', '')
            task_tag = task.tag if hasattr(task, 'tag') else task.get('tag', '')
            task_entity = task.entity if hasattr(task, 'entity') else task.get('entity')
            task_parent = task.parent if hasattr(task, 'parent') else task.get('parent')

            # Valid task ID format
            if task_id.startswith('t') and task_id[1:].isdigit():
                score += 0.1

            # Appropriate tag usage
            if task_tag in ['collect_data', 'analyze', 'visualize', 'summarize', 'general_qa']:
                score += 0.1

            # Entity field contains actual identifiers, not task references
            if task_entity and not (isinstance(task_entity, str) and task_entity.startswith('$t')):
                score += 0.1

        # Dependency validation
        task_ids = set()
        for task in tasks:
            task_id = task.id if hasattr(task, 'id') else task.get('id', '')
            task_ids.add(task_id)

        for task in tasks:
            task_parent = task.parent if hasattr(task, 'parent') else task.get('parent')
            if task_parent:
                if isinstance(task_parent, list):
                    if all(pid in task_ids for pid in task_parent):
                        score += 0.1
                elif task_parent in task_ids:
                    score += 0.1

        return min(score, 1.0)

    except Exception as e:
        print(f"⚠️ Evaluation error: {e}")
        return 0.0

# Training examples for optimization
def create_training_examples():
    """Create training examples from demo queries and expected results."""
    try:
        # Try to load training data, fallback to demo data
        training_files = [
            ('/home/<USER>/projects/office_field/trader_gpt/autogen-tradergpt/app/tools_calling_work/train_demo_queries.json',
             '/home/<USER>/projects/office_field/trader_gpt/autogen-tradergpt/app/tools_calling_work/train_demo_results.json'),
            ('/home/<USER>/projects/office_field/trader_gpt/autogen-tradergpt/app/tools_calling_work/demo_queries.json',
             '/home/<USER>/projects/office_field/trader_gpt/autogen-tradergpt/app/tools_calling_work/planner_v2_results.json')
        ]

        for queries_file, results_file in training_files:
            try:
                with open(queries_file, 'r') as f:
                    demo_queries = json.load(f)
                with open(results_file, 'r') as f:
                    expected_results = json.load(f)
                break
            except FileNotFoundError:
                continue
        else:
            print("❌ No training data files found")
            return []

        training_examples = []
        for i, query in enumerate(demo_queries[:20]):  # Use first 20 for training
            if i < len(expected_results):
                expected_tasks_data = expected_results[i].get('tasks', [])

                # Convert to Task objects for structured output
                expected_tasks = []
                for task_data in expected_tasks_data:
                    if isinstance(task_data, dict):
                        try:
                            task = Task(**task_data)
                            expected_tasks.append(task)
                        except Exception:
                            continue

                # Extract symbols for enhanced training
                extracted_symbols = symbol_extractor.extract_symbols(query)
                symbols_str = json.dumps(extracted_symbols)

                example = dspy.Example(
                    query=query,
                    extracted_symbols=symbols_str,
                    tasks=expected_tasks
                ).with_inputs('query', 'extracted_symbols')

                training_examples.append(example)

        print(f"✅ Created {len(training_examples)} training examples")
        return training_examples

    except Exception as e:
        print(f"❌ Error creating training examples: {e}")
        return []

# Enhanced optimization function with persistence
def optimize_planner(force_reoptimize: bool = False):
    """Optimize the DSPy planner using training examples with model persistence."""
    global planner

    # Check if optimized model already exists
    if not force_reoptimize and OPTIMIZED_MODEL_PATH.exists():
        print("ℹ️ Optimized model already exists. Use force_reoptimize=True to re-optimize.")
        return planner

    try:
        print("🚀 Starting planner optimization...")
        optimization_start = time.time()

        training_examples = create_training_examples()
        if not training_examples:
            print("❌ No training examples available for optimization")
            return planner

        print(f"🧠 Optimizing planner with {len(training_examples)} examples...")

        # Use BootstrapFewShot optimizer with enhanced settings
        optimizer = dspy.BootstrapFewShot(
            metric=evaluate_plan_quality,
            max_bootstrapped_demos=5,
            max_labeled_demos=3,
            max_rounds=2
        )

        # Create a fresh planner for optimization
        fresh_planner = DSPyPlanner()
        optimized_planner = optimizer.compile(fresh_planner, trainset=training_examples)

        optimization_time = time.time() - optimization_start
        print(f"✅ Planner optimization completed in {optimization_time:.2f}s!")

        # Save the optimized model
        if save_optimized_model(optimized_planner):
            planner = optimized_planner
            print("💾 Optimized model saved and loaded successfully")

        return optimized_planner

    except Exception as e:
        print(f"❌ Error during optimization: {e}")
        return planner

# Performance benchmarking function
def benchmark_performance(queries: List[str], iterations: int = 3):
    """Benchmark planner performance before and after optimization."""
    print(f"🏁 Running performance benchmark with {len(queries)} queries...")

    results = {
        'original': {'times': [], 'task_counts': []},
        'optimized': {'times': [], 'task_counts': []}
    }

    # Test original planner
    original_planner = DSPyPlanner()
    for query in queries:
        times = []
        task_counts = []
        for _ in range(iterations):
            start_time = time.time()
            tasks = get_plan(query)
            end_time = time.time()
            times.append(end_time - start_time)
            task_counts.append(len(tasks))
        results['original']['times'].extend(times)
        results['original']['task_counts'].extend(task_counts)

    # Test optimized planner (if available)
    optimized_planner = load_optimized_model()
    if optimized_planner:
        global planner
        planner = optimized_planner
        for query in queries:
            times = []
            task_counts = []
            for _ in range(iterations):
                start_time = time.time()
                tasks = get_plan(query)
                end_time = time.time()
                times.append(end_time - start_time)
                task_counts.append(len(tasks))
            results['optimized']['times'].extend(times)
            results['optimized']['task_counts'].extend(task_counts)

    # Calculate and display results
    orig_avg_time = sum(results['original']['times']) / len(results['original']['times'])
    orig_avg_tasks = sum(results['original']['task_counts']) / len(results['original']['task_counts'])

    print(f"\n📊 BENCHMARK RESULTS:")
    print(f"Original Planner:")
    print(f"  Average Time: {orig_avg_time:.3f}s")
    print(f"  Average Tasks: {orig_avg_tasks:.1f}")

    if optimized_planner:
        opt_avg_time = sum(results['optimized']['times']) / len(results['optimized']['times'])
        opt_avg_tasks = sum(results['optimized']['task_counts']) / len(results['optimized']['task_counts'])

        print(f"Optimized Planner:")
        print(f"  Average Time: {opt_avg_time:.3f}s")
        print(f"  Average Tasks: {opt_avg_tasks:.1f}")
        print(f"Performance Improvement: {((orig_avg_time - opt_avg_time) / orig_avg_time * 100):.1f}%")

    return results

if __name__ == "__main__":
    print("🚀 Enhanced DSPy Planner with Performance Optimizations")
    print("=" * 60)

    # Load demo queries from JSON file
    try:
        with open('/home/<USER>/projects/office_field/trader_gpt/autogen-tradergpt/app/tools_calling_work/demo_queries.json', 'r', encoding='utf-8') as f:
            demo_queries = json.load(f)
            # Use subset for testing
            demo_queries = demo_queries[20:30]  # Process 10 queries for testing
    except FileNotFoundError:
        print("❌ demo_queries.json not found. Please create this file with your demo queries.")
        demo_queries = []

    if not demo_queries:
        print("❌ No demo queries available. Exiting.")
        exit(1)

    # Interactive options
    print(f"\nLoaded {len(demo_queries)} demo queries for processing")
    print("\nOptions:")
    print("1. Run with current planner")
    print("2. Optimize planner first")
    print("3. Run performance benchmark")
    print("4. Force re-optimization")

    choice = input("\nEnter your choice (1-4): ").strip()

    if choice == "2":
        print("\n🧠 Starting planner optimization...")
        planner = optimize_planner()
    elif choice == "3":
        print("\n🏁 Running performance benchmark...")
        benchmark_queries = demo_queries[:5]  # Use first 5 for benchmark
        benchmark_performance(benchmark_queries)
        exit(0)
    elif choice == "4":
        print("\n🔄 Force re-optimizing planner...")
        planner = optimize_planner(force_reoptimize=True)

    # Process queries with enhanced features
    results = []
    total_start_time = time.time()

    print(f"\n🔄 Processing {len(demo_queries)} queries...")

    for i, q in enumerate(demo_queries, 1):
        print(f"\n=== Processing Demo {i}: {q[:60]}{'...' if len(q) > 60 else ''}")

        try:
            # Use enhanced get_plan with performance tracking
            result = get_plan(q, show_performance=True)

            query_result = {
                "demo_number": i,
                "query": q,
                "tasks": result,
                "status": "success",
                "task_count": len(result)
            }
            results.append(query_result)
            print(f"✅ Success - Generated {len(result)} tasks")

        except Exception as e:
            query_result = {
                "demo_number": i,
                "query": q,
                "tasks": [],
                "status": "error",
                "error": str(e),
                "task_count": 0
            }
            results.append(query_result)
            print(f"❌ Planner error: {e}")

    total_time = time.time() - total_start_time


    output_file = f"/home/<USER>/projects/office_field/trader_gpt/autogen-tradergpt/app/tools_calling_work/planner_dspy_results.json"

    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)

    # Summary statistics
    successful_queries = [r for r in results if r['status'] == 'success']
    total_tasks = sum(r['task_count'] for r in successful_queries)
    avg_tasks = total_tasks / len(successful_queries) if successful_queries else 0

    print(f"\n📊 EXECUTION SUMMARY:")
    print(f"   Total Time: {total_time:.2f}s")
    print(f"   Average Time per Query: {total_time/len(demo_queries):.3f}s")
    print(f"   Successful Queries: {len(successful_queries)}/{len(demo_queries)}")
    print(f"   Total Tasks Generated: {total_tasks}")
    print(f"   Average Tasks per Query: {avg_tasks:.1f}")
    print(f"   Results saved to: {output_file}")

    # Display complexity distribution
    complexity_counts = {}
    for result in results:
        if result['status'] == 'success':
            # This would need to be tracked during processing for full stats
            pass

    print(f"\n✅ Processing complete!")
